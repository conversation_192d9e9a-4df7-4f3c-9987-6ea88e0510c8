/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* OpenAI ChatGPT 色彩系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #f7f7f8;
    --bg-tertiary: #ececf1;
    --text-primary: #0d0d0d;
    --text-secondary: #676767;
    --text-tertiary: #8e8ea0;
    --border-light: #e5e5e5;
    --border-medium: #d1d5db;
    --accent-green: #10a37f;
    --accent-green-hover: #0d8f72;
    --accent-blue: #1a73e8;
    --sidebar-bg: #171717;
    --sidebar-text: #ececec;
    --sidebar-text-secondary: #8e8ea0;
    --sidebar-hover: #2f2f2f;
    
    /* 玻璃拟态色彩 */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: rgba(31, 38, 135, 0.37);
    
    /* 字体系统 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 间距系统 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角系统 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow: hidden;
}

/* 主应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--border-light);
    overflow: hidden;
}

.new-chat-container {
    padding: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.new-chat-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: var(--sidebar-text);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: var(--sidebar-hover);
    border-color: rgba(255, 255, 255, 0.3);
}

.new-chat-btn .icon {
    width: 16px;
    height: 16px;
}

/* 聊天历史 */
.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.history-section {
    margin-bottom: var(--spacing-lg);
}

.history-title {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--sidebar-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-sm);
    padding: 0 var(--spacing-sm);
}

.history-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: var(--font-size-sm);
    color: var(--sidebar-text);
    margin-bottom: var(--spacing-xs);
}

.history-item:hover {
    background: var(--sidebar-hover);
}

.history-item.active {
    background: var(--sidebar-hover);
    color: #ffffff;
}

.history-item .icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.history-item span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--sidebar-text);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.user-profile:hover {
    background: var(--sidebar-hover);
    border-color: rgba(255, 255, 255, 0.2);
}

.avatar {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    background: var(--accent-green);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.avatar svg {
    width: 14px;
    height: 14px;
}

.username {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
    overflow: hidden;
}

/* 顶部标题栏 */
.chat-header {
    padding: var(--spacing-md) var(--spacing-xl);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-primary);
}

.model-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.model-selector:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-medium);
}

.model-selector .icon {
    width: 16px;
    height: 16px;
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* 欢迎界面 */
.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
}

.welcome-content {
    max-width: 768px;
    text-align: center;
}

.logo-container {
    margin-bottom: var(--spacing-xl);
}

.chatgpt-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto;
    color: var(--accent-green);
}

.chatgpt-logo svg {
    width: 100%;
    height: 100%;
}

.welcome-title {
    font-size: var(--font-size-3xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.2;
}

/* 示例卡片 */
.example-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.example-card {
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.example-card:hover {
    transform: translateY(-2px);
}

.card-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
}

.example-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.example-card p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 输入区域 */
.input-container {
    padding: var(--spacing-xl);
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-light);
}

.input-wrapper {
    max-width: 768px;
    margin: 0 auto;
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    background-color: var(--bg-primary);
    transition: all 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: var(--accent-green);
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
}

.message-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    resize: none;
    min-height: 24px;
    max-height: 200px;
    overflow-y: auto;
}

.message-input::placeholder {
    color: var(--text-tertiary);
}

.input-footer {
    text-align: center;
    margin-top: var(--spacing-md);
}

.input-footer p {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    padding: var(--spacing-sm);
    cursor: pointer;
    color: var(--text-primary);
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;
}

.mobile-menu-btn:hover {
    background: var(--bg-secondary);
}

.mobile-menu-btn svg {
    width: 24px;
    height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
        position: absolute;
        left: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        z-index: 1001;
    }

    .sidebar {
        position: fixed;
        left: -260px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        box-shadow: var(--shadow-xl);
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .chat-header {
        position: relative;
        justify-content: center;
    }

    .example-cards {
        grid-template-columns: 1fr;
    }

    .welcome-title {
        font-size: var(--font-size-2xl);
    }

    .input-container {
        padding: var(--spacing-md);
    }
}
