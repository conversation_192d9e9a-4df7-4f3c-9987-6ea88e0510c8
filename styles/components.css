/* 通用按钮样式 */
.glass-button {
    background: none;
    border: none;
    cursor: pointer;
    font-family: var(--font-family);
    transition: all 0.2s ease;
}

.glass-button:focus {
    outline: none;
}

/* 消息组件样式 */
.messages-container {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
}

.message {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.message-avatar.user {
    background: var(--accent-blue);
    color: white;
}

.message-avatar.assistant {
    background: var(--accent-green);
    color: white;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    background: var(--bg-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    word-wrap: break-word;
}

.message.user .message-text {
    background: var(--accent-blue);
    color: white;
    margin-left: var(--spacing-xl);
}

.message.assistant .message-text {
    margin-right: var(--spacing-xl);
}

/* 代码块样式 */
.message-text pre {
    background: var(--bg-tertiary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    overflow-x: auto;
    margin: var(--spacing-md) 0;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: var(--font-size-sm);
}

.message-text code {
    background: var(--bg-tertiary);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875em;
}

.message-text pre code {
    background: none;
    padding: 0;
}

/* 列表样式 */
.message-text ul,
.message-text ol {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

.message-text li {
    margin-bottom: var(--spacing-xs);
}

/* 链接样式 */
.message-text a {
    color: var(--accent-blue);
    text-decoration: none;
}

.message-text a:hover {
    text-decoration: underline;
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    margin-right: var(--spacing-xl);
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-tertiary);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 错误消息样式 */
.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-sm);
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--bg-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滚动条样式 */
.chat-history::-webkit-scrollbar,
.chat-container::-webkit-scrollbar,
.message-input::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track,
.chat-container::-webkit-scrollbar-track,
.message-input::-webkit-scrollbar-track {
    background: transparent;
}

.chat-history::-webkit-scrollbar-thumb,
.chat-container::-webkit-scrollbar-thumb,
.message-input::-webkit-scrollbar-thumb {
    background: var(--border-medium);
    border-radius: var(--radius-full);
}

.chat-history::-webkit-scrollbar-thumb:hover,
.chat-container::-webkit-scrollbar-thumb:hover,
.message-input::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* 侧边栏滚动条 */
.chat-history::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .message {
        margin-bottom: var(--spacing-lg);
    }
    
    .message-text {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .message.user .message-text {
        margin-left: var(--spacing-md);
    }
    
    .message.assistant .message-text {
        margin-right: var(--spacing-md);
    }
    
    .message-avatar {
        width: 28px;
        height: 28px;
    }
}

/* 动画效果 */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 选择文本样式 */
::selection {
    background: rgba(16, 163, 127, 0.2);
}

::-moz-selection {
    background: rgba(16, 163, 127, 0.2);
}

/* 焦点样式 */
.history-item:focus,
.new-chat-btn:focus,
.user-profile:focus,
.model-selector:focus {
    outline: 2px solid var(--accent-green);
    outline-offset: 2px;
}

/* 禁用状态 */
.glass-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
