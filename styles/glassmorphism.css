/* 玻璃拟态效果样式 - 基于您的发送按钮设计 */

/* 玻璃卡片基础样式 */
.glass-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 
        0 8px 32px 0 rgba(31, 38, 135, 0.37),
        inset 1px 1px 1px rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 
        0 12px 40px 0 rgba(31, 38, 135, 0.45),
        inset 1px 1px 1px rgba(255, 255, 255, 0.15);
}

/* 玻璃输入框样式 */
.glass-input {
    background: linear-gradient(136.39deg, rgba(235, 242, 250, 0.35) 15.81%, rgba(248, 243, 255, 0.35) 70.55%, rgba(255, 255, 255, 0.35) 88.8%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 4px 16px 0 rgba(31, 38, 135, 0.2),
        inset 1px 1px 1px rgba(255, 255, 255, 0.1);
}

.glass-input:focus-within {
    background: linear-gradient(136.39deg, rgba(235, 242, 250, 0.45) 15.81%, rgba(248, 243, 255, 0.45) 70.55%, rgba(255, 255, 255, 0.45) 88.8%);
    border-color: rgba(16, 163, 127, 0.3);
    box-shadow: 
        0 0 0 3px rgba(16, 163, 127, 0.1),
        0 6px 20px 0 rgba(31, 38, 135, 0.25),
        inset 1px 1px 1px rgba(255, 255, 255, 0.15);
}

/* 发送按钮 - 1:1复制您的原始设计，只是缩小尺寸 */
.glass-send-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    position: relative;
    width: 50px;
    height: 50px;
    transition: transform 0.1s ease-out;
    flex-shrink: 0;
    font-size: 29px; /* 相当于原始的100px缩小到29% */
}

.glass-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模糊椭圆基础样式 */
.glass-send-btn .blur-ellipse {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

/* Ellipse 4 - 左上模糊圆 - 完全按原始比例 */
.glass-send-btn .ellipse-4 {
    width: 32px;  /* 110px * 0.29 */
    height: 32px;
    left: -1.5px; /* -5px * 0.29 */
    top: -4px;    /* -14px * 0.29 */
    background: #FCF5FD;
    border: 1px solid rgba(0, 0, 0, 0.1);
    filter: blur(4.5px); /* 15px * 0.29 */
}

/* Ellipse 5 - 右上模糊圆 - 完全按原始比例 */
.glass-send-btn .ellipse-5 {
    width: 32px;  /* 110px * 0.29 */
    height: 32px;
    left: 19.5px; /* 67px * 0.29 */
    top: -4px;    /* -14px * 0.29 */
    background: #EDF8FC;
    filter: blur(4.5px); /* 15px * 0.29 */
}

/* Ellipse 3 - 中心大模糊圆 - 完全按原始比例 */
.glass-send-btn .ellipse-3 {
    width: 50px;  /* 170px * 0.29 */
    height: 50px;
    left: 4px;    /* 14px * 0.29 */
    top: 4.5px;   /* 15px * 0.29 */
    background: #C1BFBF;
    filter: blur(6px); /* 20px * 0.29 */
}

/* Ellipse 2 - 底部白色模糊圆 - 完全按原始比例 */
.glass-send-btn .ellipse-2 {
    width: 35px;  /* 120px * 0.29 */
    height: 35px;
    left: 14.5px; /* 50px * 0.29 */
    top: 16px;    /* 55px * 0.29 */
    background: #FFFFFF;
    filter: blur(4.5px); /* 15px * 0.29 */
}

/* 按钮主体 - 完全按原始比例 */
.glass-send-btn .button-body {
    position: relative;
    width: 1.7em;  /* 保持原始的1.7em比例 */
    height: 1.7em;
    border-radius: 50%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    transition: box-shadow 0.2s ease-out;

    /* 基于Figma数据的精确渐变 - 完全一致 */
    background: linear-gradient(223.21deg, rgba(217, 228, 242, 0.35) 12.88%, rgba(254, 249, 253, 0.35) 84.66%);

    /* 磨砂效果 - 完全一致 */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    /* 光影效果 - 完全一致 */
    box-shadow: inset 1px 1px 1px #FFFFFF;
}

/* 添加主要背景层 - 完全一致 */
.glass-send-btn .button-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(136.39deg, #EBF2FA 15.81%, #F8F3FF 70.55%, #FFFFFF 88.8%);
    box-shadow: inset 1px 1px 1px #FFFFFF;
    z-index: -1;
}

/* 图标样式 - 完全一致 */
.glass-send-btn .icon {
    position: relative;
    z-index: 3;
    width: 0.8em;  /* 保持原始的0.8em比例 */
    height: 0.8em;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 基于Figma数据的精确阴影效果 - 完全一致 */
    filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.1));
    transition: transform 0.2s ease-out;
}

.glass-send-btn .icon svg {
    width: 100%;
    height: 100%;
    /* 添加内阴影效果 - 完全一致 */
    filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.1));
}

.glass-send-btn .icon svg path {
    /* 确保图标使用正确的渐变 - 完全一致 */
    filter: drop-shadow(inset 0px 1px 1px #FDFDFD);
}

/* 点击效果 - 完全一致 */
.glass-send-btn:active {
    transform: scale(0.96);
}

.glass-send-btn:active .button-body {
    box-shadow: inset 0.02em 0.02em 0.1em rgba(68, 71, 75, 0.2);
}

.glass-send-btn:active .icon {
    transform: translateY(0.02em);
}

/* 启用状态的发送按钮 */
.glass-send-btn:not(:disabled) .button-body {
    background: linear-gradient(223.21deg, rgba(16, 163, 127, 0.15) 12.88%, rgba(16, 163, 127, 0.05) 84.66%);
}

.glass-send-btn:not(:disabled) .button-body::before {
    background: linear-gradient(136.39deg, rgba(16, 163, 127, 0.1) 15.81%, rgba(16, 163, 127, 0.05) 70.55%, rgba(255, 255, 255, 0.9) 88.8%);
}

/* 其他玻璃效果组件 */
.glass-overlay {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-panel {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 
        0 8px 32px 0 rgba(31, 38, 135, 0.3),
        inset 1px 1px 1px rgba(255, 255, 255, 0.1);
}

/* 玻璃按钮变体 */
.glass-button-primary {
    background: linear-gradient(135deg, rgba(16, 163, 127, 0.2), rgba(16, 163, 127, 0.1));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 163, 127, 0.3);
    color: var(--accent-green);
    box-shadow: 
        0 4px 16px 0 rgba(16, 163, 127, 0.2),
        inset 1px 1px 1px rgba(255, 255, 255, 0.1);
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(16, 163, 127, 0.3), rgba(16, 163, 127, 0.15));
    border-color: rgba(16, 163, 127, 0.4);
    box-shadow: 
        0 6px 20px 0 rgba(16, 163, 127, 0.25),
        inset 1px 1px 1px rgba(255, 255, 255, 0.15);
}

.glass-button-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    box-shadow: 
        0 4px 16px 0 rgba(31, 38, 135, 0.15),
        inset 1px 1px 1px rgba(255, 255, 255, 0.1);
}

.glass-button-secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.08));
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 
        0 6px 20px 0 rgba(31, 38, 135, 0.2),
        inset 1px 1px 1px rgba(255, 255, 255, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .glass-send-btn {
        width: 36px;
        height: 36px;
    }
    
    .glass-send-btn .button-body {
        width: 36px;
        height: 36px;
    }
    
    .glass-send-btn .icon {
        width: 18px;
        height: 18px;
    }
    
    /* 调整模糊椭圆大小 */
    .glass-send-btn .ellipse-4,
    .glass-send-btn .ellipse-5 {
        width: 23px;
        height: 23px;
    }
    
    .glass-send-btn .ellipse-3 {
        width: 36px;
        height: 36px;
    }
    
    .glass-send-btn .ellipse-2 {
        width: 25px;
        height: 25px;
        left: 11px;
        top: 12px;
    }
}
