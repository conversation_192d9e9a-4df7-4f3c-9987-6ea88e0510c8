<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT 界面演示</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/glassmorphism.css">
    <style>
        /* 演示页面特殊样式 */
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
        }
        
        .demo-feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .demo-feature h3 {
            margin-bottom: 0.5rem;
        }
        
        .demo-actions {
            text-align: center;
            margin-top: 2rem;
        }
        
        .demo-btn {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem 2rem;
            border-radius: 2rem;
            text-decoration: none;
            font-weight: 600;
            margin: 0 1rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .demo-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .app-preview {
            margin: 2rem 0;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-height: 80vh;
        }
    </style>
</head>
<body>
    <!-- 演示页面头部 -->
    <div class="demo-header">
        <h1 class="demo-title">ChatGPT 界面复刻</h1>
        <p class="demo-subtitle">基于玻璃拟态风格的OpenAI ChatGPT完美复刻</p>
        
        <div class="demo-features">
            <div class="demo-feature">
                <div class="demo-feature-icon">🎨</div>
                <h3>玻璃拟态设计</h3>
                <p>精美的半透明效果和模糊背景</p>
            </div>
            <div class="demo-feature">
                <div class="demo-feature-icon">📱</div>
                <h3>完美响应式</h3>
                <p>适配所有设备尺寸</p>
            </div>
            <div class="demo-feature">
                <div class="demo-feature-icon">⚡</div>
                <h3>流畅交互</h3>
                <p>丝滑的动画和过渡效果</p>
            </div>
            <div class="demo-feature">
                <div class="demo-feature-icon">🍎</div>
                <h3>Apple美学</h3>
                <p>遵循苹果设计规范</p>
            </div>
        </div>
        
        <div class="demo-actions">
            <a href="index.html" class="demo-btn">体验完整版本</a>
            <a href="#preview" class="demo-btn">查看预览</a>
        </div>
    </div>

    <!-- 应用预览 -->
    <div id="preview" class="app-preview">
        <!-- 这里嵌入完整的ChatGPT界面 -->
        <div class="app-container">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <!-- 新建聊天按钮 -->
                <div class="new-chat-container">
                    <button class="new-chat-btn glass-button">
                        <svg class="icon" viewBox="0 0 24 24" fill="none">
                            <path d="M12 4V20M4 12H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        <span>新建聊天</span>
                    </button>
                </div>

                <!-- 聊天历史 -->
                <div class="chat-history">
                    <div class="history-section">
                        <h3 class="history-title">今天</h3>
                        <div class="history-item active">
                            <svg class="icon" viewBox="0 0 24 24" fill="none">
                                <path d="M8 12H16M8 16H16M8 8H16M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <span>界面设计演示</span>
                        </div>
                    </div>
                    
                    <div class="history-section">
                        <h3 class="history-title">昨天</h3>
                        <div class="history-item">
                            <svg class="icon" viewBox="0 0 24 24" fill="none">
                                <path d="M8 12H16M8 16H16M8 8H16M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <span>玻璃拟态按钮</span>
                        </div>
                        <div class="history-item">
                            <svg class="icon" viewBox="0 0 24 24" fill="none">
                                <path d="M8 12H16M8 16H16M8 8H16M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <span>前端开发技巧</span>
                        </div>
                    </div>
                </div>

                <!-- 底部用户信息 -->
                <div class="sidebar-footer">
                    <div class="user-profile glass-button">
                        <div class="avatar">
                            <svg viewBox="0 0 24 24" fill="none">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                        </div>
                        <span class="username">演示用户</span>
                    </div>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <main class="main-content">
                <!-- 顶部标题栏 -->
                <header class="chat-header">
                    <div class="model-selector glass-button">
                        <span>ChatGPT</span>
                        <svg class="icon" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </header>

                <!-- 聊天区域 -->
                <div class="chat-container">
                    <!-- 演示消息 -->
                    <div class="messages-container" style="display: block;">
                        <div class="message assistant">
                            <div class="message-avatar assistant">AI</div>
                            <div class="message-content">
                                <div class="message-text">
                                    您好！欢迎体验这个ChatGPT界面复刻项目。这个界面完美还原了OpenAI ChatGPT的设计，并融入了精美的玻璃拟态效果。
                                    <br><br>
                                    <strong>主要特色：</strong>
                                    <br>• 🎨 玻璃拟态风格设计
                                    <br>• 📱 完美响应式适配
                                    <br>• ⚡ 流畅的交互动画
                                    <br>• 🍎 Apple风格美学
                                </div>
                            </div>
                        </div>
                        
                        <div class="message user">
                            <div class="message-avatar user">您</div>
                            <div class="message-content">
                                <div class="message-text">
                                    这个界面设计得真不错！能介绍一下技术实现吗？
                                </div>
                            </div>
                        </div>
                        
                        <div class="message assistant">
                            <div class="message-avatar assistant">AI</div>
                            <div class="message-content">
                                <div class="message-text">
                                    当然可以！这个项目使用了以下技术：
                                    <br><br>
                                    <code>HTML5 + CSS3 + JavaScript</code>
                                    <br><br>
                                    <strong>核心技术特点：</strong>
                                    <br>• CSS Grid 和 Flexbox 布局
                                    <br>• backdrop-filter 实现玻璃拟态效果
                                    <br>• CSS 变量系统管理设计令牌
                                    <br>• 原生 JavaScript 实现交互逻辑
                                    <br>• 响应式设计适配多端设备
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-wrapper glass-input">
                        <textarea 
                            class="message-input" 
                            placeholder="在这里输入您的消息..." 
                            rows="1"
                            readonly
                        ></textarea>
                        
                        <!-- 发送按钮 -->
                        <button class="send-button glass-send-btn">
                            <!-- 模糊椭圆层 -->
                            <div class="blur-ellipse ellipse-4"></div>
                            <div class="blur-ellipse ellipse-5"></div>
                            <div class="blur-ellipse ellipse-3"></div>
                            <div class="blur-ellipse ellipse-2"></div>

                            <div class="button-body">
                                <div class="icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                        <defs>
                                            <linearGradient id="demoSendIconGradient" x1="0.177" y1="0.177" x2="0.82" y2="0.82">
                                                <stop offset="17.69%" stop-color="#CEC6D5" />
                                                <stop offset="82.01%" stop-color="#E8E1EE" />
                                            </linearGradient>
                                        </defs>
                                        <path d="M12 5L12 19M12 5L6 11M12 5L18 11" stroke="url(#demoSendIconGradient)" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                        </button>
                    </div>
                    
                    <!-- 底部提示文字 -->
                    <div class="input-footer">
                        <p>这是一个演示界面，展示ChatGPT的完美复刻效果。</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 简单的演示交互
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.querySelector('.message-input');
            const sendButton = document.querySelector('.send-button');
            
            // 移除只读属性，允许用户输入
            messageInput.removeAttribute('readonly');
            
            // 输入框自动调整高度
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
                
                // 切换发送按钮状态
                if (this.value.trim()) {
                    sendButton.disabled = false;
                    sendButton.classList.add('enabled');
                } else {
                    sendButton.disabled = true;
                    sendButton.classList.remove('enabled');
                }
            });
            
            // 平滑滚动到预览区域
            document.querySelector('a[href="#preview"]').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('preview').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
