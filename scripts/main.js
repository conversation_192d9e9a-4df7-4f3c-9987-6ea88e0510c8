// ChatGPT 界面交互逻辑
class ChatInterface {
    constructor() {
        this.messageInput = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.messagesContainer = document.querySelector('.messages-container');
        this.welcomeScreen = document.querySelector('.welcome-screen');

        this.historyItems = document.querySelectorAll('.history-item');
        this.newChatBtn = document.querySelector('.new-chat-btn');
        
        this.isTyping = false;
        this.conversationStarted = false;
        this.messageHistory = [];
        this.currentConversationId = this.generateConversationId();

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.adjustTextareaHeight();
        this.loadConversationHistory();
        this.updateCharacterCount();
    }
    
    setupEventListeners() {
        // 输入框事件
        this.messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight();
            this.toggleSendButton();
            this.updateCharacterCount();
        });
        
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey) {
                e.preventDefault();
                this.sendMessage();
            } else if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 发送按钮事件
        this.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });
        

        
        // 历史记录点击事件
        this.historyItems.forEach(item => {
            item.addEventListener('click', () => {
                this.selectHistoryItem(item);
            });
        });
        
        // 新建聊天按钮
        this.newChatBtn.addEventListener('click', () => {
            this.startNewChat();
        });
        
        // 移动端侧边栏切换
        this.setupMobileMenu();
    }
    
    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        const scrollHeight = this.messageInput.scrollHeight;
        const maxHeight = 200; // 最大高度
        
        if (scrollHeight <= maxHeight) {
            this.messageInput.style.height = scrollHeight + 'px';
        } else {
            this.messageInput.style.height = maxHeight + 'px';
        }
    }
    
    toggleSendButton() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
        
        if (hasText && !this.isTyping) {
            this.sendButton.classList.add('enabled');
        } else {
            this.sendButton.classList.remove('enabled');
        }
    }
    
    async sendMessage() {
        const text = this.messageInput.value.trim();
        if (!text || this.isTyping) return;
        
        // 如果是第一次发送消息，隐藏欢迎界面
        if (!this.conversationStarted) {
            this.startConversation();
        }
        
        // 添加用户消息
        this.addMessage(text, 'user');
        
        // 清空输入框
        this.messageInput.value = '';
        this.adjustTextareaHeight();
        this.toggleSendButton();
        
        // 显示打字指示器
        this.showTypingIndicator();
        
        // 模拟AI回复
        setTimeout(() => {
            this.hideTypingIndicator();
            this.addMessage(this.generateResponse(text), 'assistant');
        }, 1000 + Math.random() * 2000);
    }
    
    startConversation() {
        this.conversationStarted = true;
        this.welcomeScreen.style.display = 'none';
        this.messagesContainer.style.display = 'block';
        
        // 滚动到底部
        setTimeout(() => {
            this.scrollToBottom();
        }, 100);
    }
    
    addMessage(text, sender) {
        const timestamp = Date.now();

        // 保存到消息历史
        this.messageHistory.push({
            text: text,
            sender: sender,
            timestamp: timestamp
        });

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${sender}`;
        avatar.textContent = sender === 'user' ? '您' : 'AI';

        const content = document.createElement('div');
        content.className = 'message-content';

        const messageText = document.createElement('div');
        messageText.className = 'message-text';

        // 处理文本格式（简单的markdown支持）
        messageText.innerHTML = this.formatMessage(text);

        // 添加复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-message-btn';
        copyButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M8 4V16C8 17.1046 8.89543 18 10 18H18C19.1046 18 20 17.1046 20 16V7.24264C20 6.97721 19.8946 6.72281 19.7071 6.53553L16.4645 3.29289C16.2772 3.10536 16.0228 3 15.7574 3H10C8.89543 3 8 3.89543 8 5Z" stroke="currentColor" stroke-width="1.5"/>
                <path d="M16 18V20C16 21.1046 15.1046 22 14 22H6C4.89543 22 4 21.1046 4 20V9C4 7.89543 4.89543 7 6 7H8" stroke="currentColor" stroke-width="1.5"/>
            </svg>
        `;
        copyButton.title = '复制消息';
        copyButton.addEventListener('click', () => this.copyMessage(text));

        // 添加时间戳
        const timeStamp = document.createElement('div');
        timeStamp.className = 'message-timestamp';
        timeStamp.textContent = new Date(timestamp).toLocaleTimeString();

        content.appendChild(messageText);
        content.appendChild(timeStamp);
        content.appendChild(copyButton);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();

        // 保存对话历史
        this.saveConversationHistory();
    }
    
    formatMessage(text) {
        // 简单的markdown格式化
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    showTypingIndicator() {
        this.isTyping = true;
        this.toggleSendButton();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant typing-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar assistant';
        avatar.textContent = 'AI';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;
        
        content.appendChild(typingIndicator);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        this.isTyping = false;
        this.toggleSendButton();
        
        const typingMessage = this.messagesContainer.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }
    
    generateResponse(userMessage) {
        // 智能响应生成逻辑
        const responses = {
            greeting: [
                "您好！我是ChatGPT，很高兴为您服务。有什么我可以帮助您的吗？",
                "欢迎使用ChatGPT！我可以协助您解答问题、提供建议或进行对话。",
                "您好！我在这里随时准备帮助您。请告诉我您需要什么帮助。"
            ],
            design: [
                "关于设计，我建议您考虑用户体验、视觉层次和交互流程。现代设计趋向于简洁、直观和响应式。您可以参考Material Design或Apple的Human Interface Guidelines来获得灵感。",
                "设计的核心是解决问题。建议您从用户需求出发，注重可用性和美观性的平衡。色彩搭配、字体选择和布局都很重要。",
                "好的设计应该是功能性与美学的完美结合。建议您多观察优秀的设计案例，学习它们的设计思路和实现方法。"
            ],
            programming: [
                "编程是一门艺术，也是一门科学。建议您从基础语法开始，逐步学习数据结构和算法，然后实践项目开发。记住，最好的学习方式是动手实践！",
                "编程需要逻辑思维和耐心。建议您选择一门语言深入学习，掌握基本概念后再扩展到其他技术栈。多写代码，多调试，多总结。",
                "代码质量很重要。建议您养成良好的编程习惯：写清晰的注释、使用有意义的变量名、保持代码简洁。同时要学会使用版本控制工具。"
            ],
            learning: [
                "学习是一个持续的过程。我建议您制定明确的目标，分解成小的可执行步骤，保持持续的练习，并寻求反馈。记住，每个人的学习方式都不同，找到适合自己的方法很重要。",
                "有效学习需要主动思考和实践。建议您采用费曼学习法：学习-理解-教授-反思。通过教授他人来检验自己的理解程度。",
                "学习新技能时，建议您设定具体的里程碑，定期回顾进展。保持好奇心，不要害怕犯错，错误往往是最好的老师。"
            ],
            general: [
                "这是一个很有趣的问题！让我来为您详细解答。",
                "根据您的描述，我建议您可以考虑以下几个方面：",
                "感谢您的提问。这个话题确实值得深入探讨。",
                "我理解您的需求。让我为您提供一些实用的建议：",
                "这是一个很好的想法！我们可以从以下角度来分析："
            ]
        };

        // 智能分类用户消息
        const message = userMessage.toLowerCase();
        let category = 'general';

        if (message.includes('你好') || message.includes('hello') || message.includes('hi')) {
            category = 'greeting';
        } else if (message.includes('设计') || message.includes('界面') || message.includes('ui') || message.includes('ux')) {
            category = 'design';
        } else if (message.includes('编程') || message.includes('代码') || message.includes('程序') || message.includes('开发')) {
            category = 'programming';
        } else if (message.includes('学习') || message.includes('如何') || message.includes('怎么')) {
            category = 'learning';
        }

        const categoryResponses = responses[category];
        const randomResponse = categoryResponses[Math.floor(Math.random() * categoryResponses.length)];

        // 添加个性化元素
        const personalizedEnding = category === 'general' ?
            "\n\n这是一个演示回复。在实际应用中，这里会连接到真正的AI模型来生成更智能的回复。" :
            "\n\n希望这些建议对您有帮助！如果您还有其他问题，请随时告诉我。";

        return randomResponse + personalizedEnding;
    }
    
    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
    
    selectHistoryItem(item) {
        // 移除其他项目的active状态
        this.historyItems.forEach(historyItem => {
            historyItem.classList.remove('active');
        });
        
        // 添加active状态到当前项目
        item.classList.add('active');
        
        // 这里可以加载对应的聊天历史
        console.log('选择了历史记录:', item.querySelector('span').textContent);
    }
    
    startNewChat() {
        // 重置界面状态
        this.conversationStarted = false;
        this.messagesContainer.style.display = 'none';
        this.messagesContainer.innerHTML = '';
        this.welcomeScreen.style.display = 'flex';

        // 重置消息历史和对话ID
        this.messageHistory = [];
        this.currentConversationId = this.generateConversationId();

        // 清空输入框
        this.messageInput.value = '';
        this.adjustTextareaHeight();
        this.toggleSendButton();
        this.updateCharacterCount();

        // 移除历史记录的active状态
        this.historyItems.forEach(item => {
            item.classList.remove('active');
        });

        console.log('开始新聊天');
    }
    
    setupMobileMenu() {
        // 移动端菜单切换逻辑
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            // 创建菜单按钮
            const menuButton = document.createElement('button');
            menuButton.className = 'mobile-menu-btn';
            menuButton.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            `;
            
            // 添加菜单按钮到头部
            const chatHeader = document.querySelector('.chat-header');
            chatHeader.insertBefore(menuButton, chatHeader.firstChild);
            
            // 菜单切换事件
            menuButton.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });
            
            // 点击主内容区域关闭菜单
            mainContent.addEventListener('click', () => {
                sidebar.classList.remove('open');
            });
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ChatInterface();
});

// 响应式处理
window.addEventListener('resize', () => {
    // 重新设置移动端菜单
    if (window.innerWidth > 768) {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.remove('open');
    }

    // 生成对话ID
    generateConversationId() {
        return 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 更新字符计数
    updateCharacterCount() {
        const currentLength = this.messageInput.value.length;
        const maxLength = parseInt(this.messageInput.getAttribute('maxlength')) || 4000;

        // 创建或更新字符计数显示
        let charCounter = document.querySelector('.char-counter');
        if (!charCounter) {
            charCounter = document.createElement('div');
            charCounter.className = 'char-counter';
            const inputFooter = document.querySelector('.input-footer');
            inputFooter.appendChild(charCounter);
        }

        charCounter.textContent = `${currentLength}/${maxLength}`;
        charCounter.style.color = currentLength > maxLength * 0.9 ? '#ff6b6b' : '#8e8ea0';
    }

    // 保存对话历史
    saveConversationHistory() {
        const conversationData = {
            id: this.currentConversationId,
            messages: this.messageHistory,
            timestamp: Date.now(),
            title: this.generateConversationTitle()
        };

        let conversations = JSON.parse(localStorage.getItem('chatgpt_conversations') || '[]');
        const existingIndex = conversations.findIndex(conv => conv.id === this.currentConversationId);

        if (existingIndex >= 0) {
            conversations[existingIndex] = conversationData;
        } else {
            conversations.unshift(conversationData);
        }

        // 只保留最近50个对话
        conversations = conversations.slice(0, 50);
        localStorage.setItem('chatgpt_conversations', JSON.stringify(conversations));
    }

    // 加载对话历史
    loadConversationHistory() {
        const conversations = JSON.parse(localStorage.getItem('chatgpt_conversations') || '[]');
        // 这里可以更新侧边栏的历史记录显示
        console.log('已加载对话历史:', conversations.length, '个对话');
    }

    // 生成对话标题
    generateConversationTitle() {
        if (this.messageHistory.length === 0) return '新对话';

        const firstUserMessage = this.messageHistory.find(msg => msg.sender === 'user');
        if (firstUserMessage) {
            let title = firstUserMessage.text.substring(0, 30);
            if (firstUserMessage.text.length > 30) title += '...';
            return title;
        }

        return '新对话';
    }

    // 复制消息内容
    copyMessage(messageText) {
        navigator.clipboard.writeText(messageText).then(() => {
            this.showToast('消息已复制到剪贴板');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = messageText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('消息已复制到剪贴板');
        });
    }

    // 显示提示消息
    showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--text-primary);
            color: var(--bg-primary);
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateY(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // 导出对话
    exportConversation() {
        if (this.messageHistory.length === 0) {
            this.showToast('没有可导出的对话内容');
            return;
        }

        const conversationText = this.messageHistory.map(msg => {
            const timestamp = new Date(msg.timestamp).toLocaleString();
            return `[${timestamp}] ${msg.sender === 'user' ? '用户' : 'ChatGPT'}: ${msg.text}`;
        }).join('\n\n');

        const blob = new Blob([conversationText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ChatGPT对话_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('对话已导出');
    }
});
