// ChatGPT 界面交互逻辑
class ChatInterface {
    constructor() {
        this.messageInput = document.querySelector('.message-input');
        this.sendButton = document.querySelector('.send-button');
        this.messagesContainer = document.querySelector('.messages-container');
        this.welcomeScreen = document.querySelector('.welcome-screen');
        this.exampleCards = document.querySelectorAll('.example-card');
        this.historyItems = document.querySelectorAll('.history-item');
        this.newChatBtn = document.querySelector('.new-chat-btn');
        
        this.isTyping = false;
        this.conversationStarted = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.adjustTextareaHeight();
    }
    
    setupEventListeners() {
        // 输入框事件
        this.messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight();
            this.toggleSendButton();
        });
        
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 发送按钮事件
        this.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });
        
        // 示例卡片点击事件
        this.exampleCards.forEach(card => {
            card.addEventListener('click', () => {
                const text = card.querySelector('p').textContent;
                this.messageInput.value = text;
                this.toggleSendButton();
                this.messageInput.focus();
            });
        });
        
        // 历史记录点击事件
        this.historyItems.forEach(item => {
            item.addEventListener('click', () => {
                this.selectHistoryItem(item);
            });
        });
        
        // 新建聊天按钮
        this.newChatBtn.addEventListener('click', () => {
            this.startNewChat();
        });
        
        // 移动端侧边栏切换
        this.setupMobileMenu();
    }
    
    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        const scrollHeight = this.messageInput.scrollHeight;
        const maxHeight = 200; // 最大高度
        
        if (scrollHeight <= maxHeight) {
            this.messageInput.style.height = scrollHeight + 'px';
        } else {
            this.messageInput.style.height = maxHeight + 'px';
        }
    }
    
    toggleSendButton() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText || this.isTyping;
        
        if (hasText && !this.isTyping) {
            this.sendButton.classList.add('enabled');
        } else {
            this.sendButton.classList.remove('enabled');
        }
    }
    
    async sendMessage() {
        const text = this.messageInput.value.trim();
        if (!text || this.isTyping) return;
        
        // 如果是第一次发送消息，隐藏欢迎界面
        if (!this.conversationStarted) {
            this.startConversation();
        }
        
        // 添加用户消息
        this.addMessage(text, 'user');
        
        // 清空输入框
        this.messageInput.value = '';
        this.adjustTextareaHeight();
        this.toggleSendButton();
        
        // 显示打字指示器
        this.showTypingIndicator();
        
        // 模拟AI回复
        setTimeout(() => {
            this.hideTypingIndicator();
            this.addMessage(this.generateResponse(text), 'assistant');
        }, 1000 + Math.random() * 2000);
    }
    
    startConversation() {
        this.conversationStarted = true;
        this.welcomeScreen.style.display = 'none';
        this.messagesContainer.style.display = 'block';
        
        // 滚动到底部
        setTimeout(() => {
            this.scrollToBottom();
        }, 100);
    }
    
    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${sender}`;
        avatar.textContent = sender === 'user' ? '您' : 'AI';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        
        // 处理文本格式（简单的markdown支持）
        messageText.innerHTML = this.formatMessage(text);
        
        content.appendChild(messageText);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    formatMessage(text) {
        // 简单的markdown格式化
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    showTypingIndicator() {
        this.isTyping = true;
        this.toggleSendButton();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant typing-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar assistant';
        avatar.textContent = 'AI';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const typingIndicator = document.createElement('div');
        typingIndicator.className = 'typing-indicator';
        typingIndicator.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;
        
        content.appendChild(typingIndicator);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        this.isTyping = false;
        this.toggleSendButton();
        
        const typingMessage = this.messagesContainer.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }
    
    generateResponse(userMessage) {
        // 简单的响应生成逻辑
        const responses = [
            "这是一个很有趣的问题！让我来为您详细解答...",
            "根据您的描述，我建议您可以考虑以下几个方面：",
            "感谢您的提问。这个话题确实值得深入探讨。",
            "我理解您的需求。让我为您提供一些实用的建议：",
            "这是一个很好的想法！我们可以从以下角度来分析：",
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        
        // 根据用户输入生成更相关的回复
        if (userMessage.includes('设计') || userMessage.includes('界面')) {
            return "关于设计，我建议您考虑用户体验、视觉层次和交互流程。现代设计趋向于简洁、直观和响应式。您可以参考Material Design或Apple的Human Interface Guidelines来获得灵感。";
        } else if (userMessage.includes('编程') || userMessage.includes('代码')) {
            return "编程是一门艺术，也是一门科学。建议您从基础语法开始，逐步学习数据结构和算法，然后实践项目开发。记住，最好的学习方式是动手实践！";
        } else if (userMessage.includes('学习') || userMessage.includes('如何')) {
            return "学习是一个持续的过程。我建议您制定明确的目标，分解成小的可执行步骤，保持持续的练习，并寻求反馈。记住，每个人的学习方式都不同，找到适合自己的方法很重要。";
        }
        
        return randomResponse + "\n\n这只是一个演示回复。在实际应用中，这里会连接到真正的AI模型来生成更智能的回复。";
    }
    
    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
    
    selectHistoryItem(item) {
        // 移除其他项目的active状态
        this.historyItems.forEach(historyItem => {
            historyItem.classList.remove('active');
        });
        
        // 添加active状态到当前项目
        item.classList.add('active');
        
        // 这里可以加载对应的聊天历史
        console.log('选择了历史记录:', item.querySelector('span').textContent);
    }
    
    startNewChat() {
        // 重置界面状态
        this.conversationStarted = false;
        this.messagesContainer.style.display = 'none';
        this.messagesContainer.innerHTML = '';
        this.welcomeScreen.style.display = 'flex';
        
        // 清空输入框
        this.messageInput.value = '';
        this.adjustTextareaHeight();
        this.toggleSendButton();
        
        // 移除历史记录的active状态
        this.historyItems.forEach(item => {
            item.classList.remove('active');
        });
        
        console.log('开始新聊天');
    }
    
    setupMobileMenu() {
        // 移动端菜单切换逻辑
        if (window.innerWidth <= 768) {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            // 创建菜单按钮
            const menuButton = document.createElement('button');
            menuButton.className = 'mobile-menu-btn';
            menuButton.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            `;
            
            // 添加菜单按钮到头部
            const chatHeader = document.querySelector('.chat-header');
            chatHeader.insertBefore(menuButton, chatHeader.firstChild);
            
            // 菜单切换事件
            menuButton.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });
            
            // 点击主内容区域关闭菜单
            mainContent.addEventListener('click', () => {
                sidebar.classList.remove('open');
            });
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ChatInterface();
});

// 响应式处理
window.addEventListener('resize', () => {
    // 重新设置移动端菜单
    if (window.innerWidth > 768) {
        const sidebar = document.querySelector('.sidebar');
        sidebar.classList.remove('open');
    }
});
